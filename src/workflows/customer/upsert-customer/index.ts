import { CustomerDTO } from "@medusajs/framework/types";
import { Modules } from "@medusajs/framework/utils";
import {
    createWorkflow,
    when,
    WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import {
    createRemoteLinkStep,
    useQueryGraphStep,
} from "@medusajs/medusa/core-flows";
import { checkCustomerTagsExistenceStep } from "../steps/check-customer-tags-existance";
import { EXTENDED_CUSTOMER_MODULE } from "../../../modules/extended/customer";
import { upsertExtendedCustomerStep } from "./steps/upsert-extended-customer";
import { upsertCustomerStep } from "./steps/upsert-customer";
import { CustomerDTOWithModifiedAddress } from "../../../scripts/customer-migration";
// import { testStep } from "./steps/test-step";

export type UpsertCustomerWorkflowInput = {
    customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any }
};

export const upsertCustomer = createWorkflow(
    'upsert-customer',
    (input: UpsertCustomerWorkflowInput) => {
        // Log workflow start
        console.log(`[UPSERT-CUSTOMER-WORKFLOW] Starting workflow for customer ID: ${input.customer.id}`);
        console.log(`[UPSERT-CUSTOMER-WORKFLOW] Customer email: ${input.customer.email}, phone: ${input.customer.phone}`);
        console.log(`[UPSERT-CUSTOMER-WORKFLOW] Additional data keys: ${Object.keys(input.customer.additional_data || {}).join(', ')}`);

        // Validate customer tags if provided
        // checkCustomerTagsExistenceStep({
        //     tag_ids: input.customer.additional_data?.customer_tag_ids,
        // });

        // Upsert the main customer record
        console.log(`[UPSERT-CUSTOMER-WORKFLOW] Step 1: Upserting main customer record`);
        const customer = upsertCustomerStep(input.customer);

        // Query for existing extended customer data

        // testStep(customers);

        // Handle extended customer data if additional_data is provided
        // const extendedCustomer = when(
        //     { input, customers, customer },
        //     data => !!data.customers[0].id
        // ).then(() => {
        //     return upsertExtendedCustomerStep({
        //         customer: customers[0],
        //         additional_data: input.customer.additional_data,
        //     });
        // });

        console.log(`[UPSERT-CUSTOMER-WORKFLOW] Step 2: Upserting extended customer data`);
        const extendedCustomer = upsertExtendedCustomerStep({
            customer_id: customer.id,
            additional_data: input.customer.additional_data,
        });

        // Create remote link if extended customer was created and no link exists
        console.log(`[UPSERT-CUSTOMER-WORKFLOW] Step 3: Creating remote link between customer and extended customer`);
        createRemoteLinkStep([
            {
                [Modules.CUSTOMER]: { customer_id: customer.id },
                [EXTENDED_CUSTOMER_MODULE]: {
                    extended_customer_id: extendedCustomer.id,
                },
            },
        ]);

        console.log(`[UPSERT-CUSTOMER-WORKFLOW] Workflow completed successfully`);
        return new WorkflowResponse({
            customer,
            extended_customer: extendedCustomer,
        });
    }
);

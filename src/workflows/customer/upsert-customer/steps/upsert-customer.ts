import { CustomerDTO } from "@medusajs/framework/types";
import { deepEqualObj, Modules } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { CustomerDTOWithModifiedAddress } from "../../../../scripts/customer-migration";
import { logStepOperation, logStepError, isLoggerInitialized } from "../../../../scripts/utils/logger";

export const upsertCustomerStep = createStep(
  'upsert-customer',
  async (customer: CustomerDTOWithModifiedAddress<'id'>, { container }) => {
    const customerService = container.resolve(Modules.CUSTOMER);
    const logger = container.resolve('logger');

    const startMessage = `Starting upsert for customer ID: ${customer.id}`;
    const detailsMessage = `Customer details: email=${customer.email}, phone=${customer.phone}, addresses=${customer.addresses?.length || 0}`;

    logger.info(`[UPSERT-CUSTOMER] ${startMessage}`);
    logger.info(`[UPSERT-CUSTOMER] ${detailsMessage}`);

    // Also log to file if logger is initialized
    if (isLoggerInitialized()) {
      await logStepOperation('UPSERT-CUSTOMER', startMessage, { customerId: customer.id, email: customer.email, phone: customer.phone });
      await logStepOperation('UPSERT-CUSTOMER', detailsMessage, { addressCount: customer.addresses?.length || 0 });
    }

    let existingCustomer: any = null;
    let upsertedCustomer: CustomerDTO | null = null;
    const operationStartTime = Date.now();

    try {
      // Only check for existing customer by ID if provided
      if (customer.id) {
        const checkMessage = `Checking for existing customer with ID: ${customer.id}`;
        logger.info(`[UPSERT-CUSTOMER] ${checkMessage}`);
        if (isLoggerInitialized()) {
          await logStepOperation('UPSERT-CUSTOMER', checkMessage, { customerId: customer.id });
        }

        try {
          const retrieveStartTime = Date.now();
          existingCustomer = await customerService.retrieveCustomer(customer.id);
          const retrieveTime = Date.now() - retrieveStartTime;

          const foundMessage = `Found existing customer by ID: ${customer.id} (retrieve time: ${retrieveTime}ms)`;
          const existingDetailsMessage = `Existing customer details: email=${existingCustomer.email}, phone=${existingCustomer.phone}`;

          logger.info(`[UPSERT-CUSTOMER] ${foundMessage}`);
          logger.info(`[UPSERT-CUSTOMER] ${existingDetailsMessage}`);

          if (isLoggerInitialized()) {
            await logStepOperation('UPSERT-CUSTOMER', foundMessage, { customerId: customer.id, retrieveTime, existingEmail: existingCustomer.email });
            await logStepOperation('UPSERT-CUSTOMER', existingDetailsMessage, { existingCustomer: { email: existingCustomer.email, phone: existingCustomer.phone } });
          }
        } catch (error) {
          // Customer with ID doesn't exist, will create new one
          const retrieveTime = Date.now() - operationStartTime;
          const notFoundMessage = `Customer with ID ${customer.id} not found, will create new customer (check time: ${retrieveTime}ms)`;

          logger.info(`[UPSERT-CUSTOMER] ${notFoundMessage}`);
          if (isLoggerInitialized()) {
            await logStepOperation('UPSERT-CUSTOMER', notFoundMessage, { customerId: customer.id, checkTime: retrieveTime });
          }
        }
      } else {
        const noIdMessage = `No customer ID provided, will create new customer`;
        logger.info(`[UPSERT-CUSTOMER] ${noIdMessage}`);
        if (isLoggerInitialized()) {
          await logStepOperation('UPSERT-CUSTOMER', noIdMessage, {});
        }
      }

      if (existingCustomer) {
        // Update existing customer
        const updateStartTime = Date.now();
        logger.info(`[UPSERT-CUSTOMER] Updating existing customer: ${existingCustomer.id}`);
        logger.info(`[UPSERT-CUSTOMER] Existing customer details: ${JSON.stringify({
          id: existingCustomer.id,
          email: existingCustomer.email,
          phone: existingCustomer.phone,
          first_name: existingCustomer.first_name,
          last_name: existingCustomer.last_name
        })}`);

        // Exclude addresses from update data to avoid conflicts
        const { addresses, ...updateData } = customer;
        logger.info(`[UPSERT-CUSTOMER] Update data (excluding addresses): ${JSON.stringify({
          email: updateData.email,
          phone: updateData.phone,
          first_name: updateData.first_name,
          last_name: updateData.last_name
        })}`);

        const customerUpdateStartTime = Date.now();
        upsertedCustomer = await customerService.updateCustomers(existingCustomer.id, updateData);
        const customerUpdateTime = Date.now() - customerUpdateStartTime;
        logger.info(`[UPSERT-CUSTOMER] Customer updated successfully in ${customerUpdateTime}ms`);

        // Handle addresses
        logger.info(`[UPSERT-CUSTOMER] Managing customer addresses`);
        const addressListStartTime = Date.now();
        const customerAddresses = await customerService.listCustomerAddresses({
          customer_id: existingCustomer.id,
        });
        const addressListTime = Date.now() - addressListStartTime;
        logger.info(`[UPSERT-CUSTOMER] Found ${customerAddresses.length} existing addresses (list time: ${addressListTime}ms)`);

        // Delete existing addresses
        if (customerAddresses.length > 0) {
          const addressIds = customerAddresses.map((address: any) => address.id);
          logger.info(`[UPSERT-CUSTOMER] Deleting ${addressIds.length} existing addresses: ${addressIds.join(', ')}`);
          const deleteStartTime = Date.now();
          await customerService.deleteCustomerAddresses(addressIds);
          const deleteTime = Date.now() - deleteStartTime;
          logger.info(`[UPSERT-CUSTOMER] Deleted existing addresses in ${deleteTime}ms`);
        }

        // Add new addresses
        if (customer.addresses && customer.addresses.length > 0) {
          logger.info(`[UPSERT-CUSTOMER] Creating ${customer.addresses.length} new addresses`);
          const createAddressStartTime = Date.now();
          const newAddresses = customer.addresses.map((address: any) => ({
            ...address,
            customer_id: customer.id,
          }));
          await customerService.createCustomerAddresses(newAddresses);
          const createAddressTime = Date.now() - createAddressStartTime;
          logger.info(`[UPSERT-CUSTOMER] Created new addresses in ${createAddressTime}ms`);
        } else {
          logger.info(`[UPSERT-CUSTOMER] No new addresses to create`);
        }

        const totalUpdateTime = Date.now() - updateStartTime;
        logger.info(`[UPSERT-CUSTOMER] Customer update completed in ${totalUpdateTime}ms`);

      } else {
        // Create new customer
        const createStartTime = Date.now();
        logger.info(`[UPSERT-CUSTOMER] Creating new customer`);
        logger.info(`[UPSERT-CUSTOMER] Customer data for creation: ${JSON.stringify({
          email: customer.email,
          phone: customer.phone,
          first_name: customer.first_name,
          last_name: customer.last_name,
          addressCount: customer.addresses?.length || 0
        })}`);

        const createData = { ...customer, id: undefined }; // Remove ID for creation

        try {
          const customerCreateStartTime = Date.now();
          upsertedCustomer = await customerService.createCustomers(customer);
          const customerCreateTime = Date.now() - customerCreateStartTime;
          logger.info(`[UPSERT-CUSTOMER] Customer created successfully with ID: ${upsertedCustomer.id} (create time: ${customerCreateTime}ms)`);
        } catch (error: any) {
          // Handle email duplication case
          if (error.message && error.message.includes('email') && error.message.includes('already exists')) {
            logger.info(`[UPSERT-CUSTOMER] Customer with email ${customer.email} already exists. Creating customer without email.`);
            const customerWithoutEmail = { ...createData, email: undefined };
            logger.info(`[UPSERT-CUSTOMER] Retry data (without email): ${JSON.stringify({
              phone: customerWithoutEmail.phone,
              first_name: customerWithoutEmail.first_name,
              last_name: customerWithoutEmail.last_name
            })}`);

            const retryCreateStartTime = Date.now();
            upsertedCustomer = await customerService.createCustomers(customerWithoutEmail);
            const retryCreateTime = Date.now() - retryCreateStartTime;
            logger.info(`[UPSERT-CUSTOMER] Customer created without email with ID: ${upsertedCustomer.id} (retry create time: ${retryCreateTime}ms)`);
          } else {
            logger.error(`[UPSERT-CUSTOMER] Failed to create customer: ${error.message}`);
            throw error;
          }
        }

        const totalCreateTime = Date.now() - createStartTime;
        logger.info(`[UPSERT-CUSTOMER] Customer creation completed in ${totalCreateTime}ms`);
      }

      if (!upsertedCustomer) {
        const failMessage = `Failed to upsert customer - no customer object returned`;
        logger.error(`[UPSERT-CUSTOMER] ${failMessage}`);
        if (isLoggerInitialized()) {
          await logStepError('UPSERT-CUSTOMER', 'upsert_customer', new Error(failMessage), { customerId: customer.id });
        }
        throw new Error('Failed to upsert customer');
      }

      const totalOperationTime = Date.now() - operationStartTime;
      const successMessage = `Successfully upserted customer: ${upsertedCustomer.id} (total operation time: ${totalOperationTime}ms)`;
      const finalDetails = {
        id: upsertedCustomer.id,
        email: upsertedCustomer.email,
        phone: upsertedCustomer.phone,
        wasUpdate: !!existingCustomer
      };

      logger.info(`[UPSERT-CUSTOMER] ${successMessage}`);
      logger.info(`[UPSERT-CUSTOMER] Final customer details: ${JSON.stringify(finalDetails)}`);

      if (isLoggerInitialized()) {
        await logStepOperation('UPSERT-CUSTOMER', successMessage, {
          customerId: upsertedCustomer.id,
          totalOperationTime,
          wasUpdate: !!existingCustomer,
          finalDetails
        });
      }

      return new StepResponse(upsertedCustomer, {
        customerId: upsertedCustomer.id,
        wasUpdate: !!existingCustomer,
        previousData: existingCustomer
      });

    } catch (error) {
      const totalOperationTime = Date.now() - operationStartTime;
      const errorMessage = `Error upserting customer after ${totalOperationTime}ms`;

      logger.error(`[UPSERT-CUSTOMER] ${errorMessage}:`, error);
      if (isLoggerInitialized()) {
        await logStepError('UPSERT-CUSTOMER', 'upsert_customer', error, {
          customerId: customer.id,
          totalOperationTime,
          wasUpdate: !!existingCustomer
        });
      }
      throw error;
    }
  },
  async (compensationData, { container }) => {
    if (!compensationData) {
      const logger = container.resolve('logger');
      logger.info(`[UPSERT-CUSTOMER-COMPENSATION] No compensation data provided, skipping compensation`);
      return;
    }

    const customerService = container.resolve(Modules.CUSTOMER);
    const logger = container.resolve('logger');
    const compensationStartTime = Date.now();

    logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Starting compensation for customer: ${compensationData.customerId}`);
    logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Compensation type: ${compensationData.wasUpdate ? 'REVERT_UPDATE' : 'DELETE_CREATED'}`);

    try {
      if (compensationData.wasUpdate && compensationData.previousData) {
        // Revert to previous data
        logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Reverting customer update: ${compensationData.customerId}`);
        logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Previous data: ${JSON.stringify({
          email: compensationData.previousData.email,
          phone: compensationData.previousData.phone,
          first_name: compensationData.previousData.first_name,
          last_name: compensationData.previousData.last_name
        })}`);

        const revertStartTime = Date.now();
        await customerService.updateCustomers(compensationData.customerId, compensationData.previousData);
        const revertTime = Date.now() - revertStartTime;
        logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Customer update reverted successfully in ${revertTime}ms`);

      } else if (!compensationData.wasUpdate) {
        // Delete the created customer
        logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Deleting created customer: ${compensationData.customerId}`);

        const deleteStartTime = Date.now();
        await customerService.deleteCustomers([compensationData.customerId]);
        const deleteTime = Date.now() - deleteStartTime;
        logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Created customer deleted successfully in ${deleteTime}ms`);
      } else {
        logger.warn(`[UPSERT-CUSTOMER-COMPENSATION] Unknown compensation scenario for customer: ${compensationData.customerId}`);
      }

      const totalCompensationTime = Date.now() - compensationStartTime;
      logger.info(`[UPSERT-CUSTOMER-COMPENSATION] Compensation completed successfully in ${totalCompensationTime}ms`);

    } catch (error) {
      const totalCompensationTime = Date.now() - compensationStartTime;
      logger.error(`[UPSERT-CUSTOMER-COMPENSATION] Error in customer upsert compensation after ${totalCompensationTime}ms:`, error);
    }
  }
);

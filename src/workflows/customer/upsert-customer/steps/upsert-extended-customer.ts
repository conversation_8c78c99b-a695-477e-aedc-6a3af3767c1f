import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { CustomerDTOWithModifiedAddress } from "../../../../scripts/customer-migration";
import { EXTENDED_CUSTOMER_MODULE } from "../../../../modules/extended/customer";
import ExtendedCustomerService from "../../../../modules/extended/customer/service";
import { CustomerDTO, InferTypeOf } from "@medusajs/framework/types";
import ExtendedCustomer from "../../../../modules/extended/customer/models/extended-customer";
import { Modules } from "@medusajs/framework/utils";
import { Customer } from "../../../../../.medusa/types/query-entry-points";

type Input = {
  customer_id: string,
  additional_data: any;
}

export const upsertExtendedCustomerStep = createStep(
  'upsert-extended-customer',
  async (input: Input, { container }) => {
    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    );
    const logger = container.resolve('logger');
    const query = container.resolve('query');
    const operationStartTime = Date.now();

    logger.info(`[UPSERT-EXTENDED-CUSTOMER] Starting upsert for customer_id: ${input.customer_id}`);
    logger.info(`[UPSERT-EXTENDED-CUSTOMER] Additional data keys: ${Object.keys(input.additional_data || {}).join(', ')}`);

    // Query for existing customer and extended customer data
    const queryStartTime = Date.now();
    const { data: [customer] } = await query.graph({
      entity: 'customer',
      fields: ['*', 'extended_customer.*'],
      filters: { id: input.customer_id },
    });
    const queryTime = Date.now() - queryStartTime;

    if (!customer) {
      logger.error(`[UPSERT-EXTENDED-CUSTOMER] Customer not found with ID: ${input.customer_id}`);
      throw new Error(`Customer not found with ID: ${input.customer_id}`);
    }

    logger.info(`[UPSERT-EXTENDED-CUSTOMER] Customer query completed in ${queryTime}ms`);
    logger.info(`[UPSERT-EXTENDED-CUSTOMER] Customer found: ${customer.id}, has extended_customer: ${!!customer.extended_customer}`);

    const link = container.resolve('link');

    try {
      let extendedCustomer: any;
      let wasUpdate = false;
      let previousData: any = null;

      if (customer.extended_customer) {
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Existing extended customer found: ${customer.extended_customer.id}`);
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Existing extended customer data: ${JSON.stringify(customer.extended_customer)}`);

        // Delete the previous and create the new object
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Deleting existing extended customer: ${customer.extended_customer.id}`);
        const deleteStartTime = Date.now();
        await extendedCustomerService.deleteExtendedCustomers(customer.extended_customer.id);
        const deleteTime = Date.now() - deleteStartTime;
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Extended customer deleted in ${deleteTime}ms`);

        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Deleting existing link for customer: ${customer.id}`);
        const linkDeleteStartTime = Date.now();
        await link.dismiss({
          [Modules.CUSTOMER]: { customer_id: customer.id },
        });
        const linkDeleteTime = Date.now() - linkDeleteStartTime;
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Link deleted in ${linkDeleteTime}ms`);

        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Creating new extended customer with data: ${JSON.stringify(input.additional_data)}`);
        const createStartTime = Date.now();
        extendedCustomer = await extendedCustomerService.createExtendedCustomers({
          ...input.additional_data
        });
        const createTime = Date.now() - createStartTime;
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] New extended customer created with ID: ${extendedCustomer.id} in ${createTime}ms`);

        wasUpdate = true;
        previousData = customer.extended_customer;

      } else {
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] No existing extended customer, creating new one`);
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Creating extended customer with data: ${JSON.stringify(input.additional_data)}`);

        const createStartTime = Date.now();
        extendedCustomer = await extendedCustomerService.createExtendedCustomers({
          ...input.additional_data
        });
        const createTime = Date.now() - createStartTime;
        logger.info(`[UPSERT-EXTENDED-CUSTOMER] Extended customer created with ID: ${extendedCustomer.id} in ${createTime}ms`);
      }

      const totalOperationTime = Date.now() - operationStartTime;
      logger.info(`[UPSERT-EXTENDED-CUSTOMER] Successfully upserted extended customer: ${extendedCustomer.id} (total operation time: ${totalOperationTime}ms)`);
      logger.info(`[UPSERT-EXTENDED-CUSTOMER] Final extended customer details: ${JSON.stringify({
        id: extendedCustomer.id,
        wasUpdate,
        hasPreviousData: !!previousData
      })}`);

      return new StepResponse(extendedCustomer, {
        extendedCustomerId: extendedCustomer.id,
        wasUpdate,
        previousData
      });

    } catch (error) {
      const totalOperationTime = Date.now() - operationStartTime;
      logger.error(`[UPSERT-EXTENDED-CUSTOMER] Error upserting extended customer after ${totalOperationTime}ms:`, error);
      throw error;
    }
  }
)
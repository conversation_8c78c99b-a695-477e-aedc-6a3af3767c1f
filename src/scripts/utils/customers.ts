import { Modules } from "@medusajs/framework/utils";
import { EXTENDED_CUSTOMER_MODULE } from "../../modules/extended/customer";
import { CustomerDTOWithModifiedAddress, servicePack } from "../customer-migration";
import { upsertCustomer } from "../../workflows/customer/upsert-customer";

export interface DatabaseOperationResult {
  message: string;
  customerId: string;
  extendedCustomerId: string;
  wasUpdate: boolean;
  operationTime: number;
  workflowTime: number;
}

export const createItemInDB = async (
  customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any },
  services: servicePack,
  container: any
): Promise<DatabaseOperationResult> => {
  const { customerService, extendedCustomerService, link, query } = services;
  const operationStartTime = Date.now();

  // Note: Using console.log here since this is called from the test script which handles file logging
  console.log(`[CREATE-ITEM-IN-DB] Starting database operation for customer ID: ${customer.id}`);
  console.log(`[CREATE-ITEM-IN-DB] Customer details: email=${customer.email}, phone=${customer.phone}`);
  console.log(`[CREATE-ITEM-IN-DB] Additional data keys: ${Object.keys(customer.additional_data || {}).join(', ')}`);

  try {
    console.log(`[CREATE-ITEM-IN-DB] Executing upsert customer workflow`);
    const workflowStartTime = Date.now();

    const customer_ = await upsertCustomer(container).run({
      input: {
        customer,
      }
    });

    const workflowTime = Date.now() - workflowStartTime;
    const totalOperationTime = Date.now() - operationStartTime;

    console.log(`[CREATE-ITEM-IN-DB] Upsert customer workflow completed in ${workflowTime}ms`);
    console.log(`[CREATE-ITEM-IN-DB] Workflow result: customer_id=${customer_.result.customer.id}, extended_customer_id=${customer_.result.extended_customer.id}`);

    // Determine if this was an update by checking if the customer already existed
    // This is a simplified check - in a real scenario you might want to pass this info from the workflow
    const wasUpdate = customer.id === customer_.result.customer.id;

    const msg = `Customer ${customer.id} processed successfully in ${totalOperationTime}ms (workflow: ${workflowTime}ms)`;
    console.log(`[CREATE-ITEM-IN-DB] ${msg}`);

    return {
      message: msg,
      customerId: customer_.result.customer.id,
      extendedCustomerId: customer_.result.extended_customer.id,
      wasUpdate,
      operationTime: totalOperationTime,
      workflowTime
    };

  } catch (error) {
    const totalOperationTime = Date.now() - operationStartTime;
    console.error(`[CREATE-ITEM-IN-DB] Error processing customer ${customer.id} after ${totalOperationTime}ms:`, error);
    throw error;
  }

};

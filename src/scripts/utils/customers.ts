import { Modules } from "@medusajs/framework/utils";
import { EXTENDED_CUSTOMER_MODULE } from "../../modules/extended/customer";
import { CustomerDTOWithModifiedAddress, servicePack } from "../customer-migration";
import { upsertCustomer } from "../../workflows/customer/upsert-customer";

export const createItemInDB = async (
  customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any },
  services: servicePack,
  container: any
) => {
  const { customerService, extendedCustomerService, link, query } = services;
  let msg = 'SUCCESS';
  const operationStartTime = Date.now();

  // Note: Using console.log here since this is called from the test script which handles file logging
  console.log(`[CREATE-ITEM-IN-DB] Starting database operation for customer ID: ${customer.id}`);
  console.log(`[CREATE-ITEM-IN-DB] Customer details: email=${customer.email}, phone=${customer.phone}`);
  console.log(`[CREATE-ITEM-IN-DB] Additional data keys: ${Object.keys(customer.additional_data || {}).join(', ')}`);

  try {
    console.log(`[CREATE-ITEM-IN-DB] Executing upsert customer workflow`);
    const workflowStartTime = Date.now();

    const customer_ = await upsertCustomer(container).run({
      input: {
        customer,
      }
    });

    const workflowTime = Date.now() - workflowStartTime;
    console.log(`[CREATE-ITEM-IN-DB] Upsert customer workflow completed in ${workflowTime}ms`);
    console.log(`[CREATE-ITEM-IN-DB] Workflow result: customer_id=${customer_.result.customer.id}, extended_customer_id=${customer_.result.extended_customer.id}`);

    const totalOperationTime = Date.now() - operationStartTime;
    msg = `Customer ${customer.id} processed successfully in ${totalOperationTime}ms (workflow: ${workflowTime}ms)`;
    console.log(`[CREATE-ITEM-IN-DB] ${msg}`);

  } catch (error) {
    const totalOperationTime = Date.now() - operationStartTime;
    console.error(`[CREATE-ITEM-IN-DB] Error processing customer ${customer.id} after ${totalOperationTime}ms:`, error);
    throw error;
  }

  // Legacy code commented out - now using workflow approach
  // const { data: existingCustomersByPhone } = await query.graph({
  //   entity: 'customer',
  //   fields: ['id', 'phone'],
  //   filters: {
  //     //@ts-ignore
  //     phone: customer.phone
  //   }
  // });

  // const existingPhoneCustomer = existingCustomersByPhone?.[0];

  // if (existingPhoneCustomer && existingPhoneCustomer.id !== customer.id) {
  //   throw new Error(`Customer with phone ${customer.phone} already exists in the database.`);
  // }

  // try {
  //     console.log('---> Creating customer :::', customer);
  //   createdCustomer = await customerService.createCustomers(customer);
  // } catch (error) {
  //   if (error instanceof Error) {
  //     if (error.message.startsWith('Customer with id') && error.message.endsWith('already exists.')) {
  //       const { id, ...customerData } = customer;
  //       createdCustomer = await customerService.updateCustomers(id, customerData);

  //       const { data: [existingExtendedCustomer] } = await query.graph({
  //         entity: 'extended_customer',
  //         fields: ['id'],
  //         filters: {
  //           //@ts-ignore
  //           customer: { id: createdCustomer.id }
  //         }
  //       });

  //       if (existingExtendedCustomer) {
  //         await extendedCustomerService.softDeleteExtendedCustomers(existingExtendedCustomer.id);
  //       }

  //       await link.delete({
  //         [Modules.CUSTOMER]: {
  //           customer_id: createdCustomer.id,
  //         },
  //       });

  //       msg = `Customer with id ${customer.id} already exists. Updated existing customer.`;
  //     } else if (error.message.startsWith('Customer with email') && error.message.endsWith('already exists.')) {
  //       const customerWithoutEmail = { ...customer, email: undefined };
  //       createdCustomer = await customerService.createCustomers(customerWithoutEmail);
  //       msg = `Customer with email ${customer.email} already exists. Created new customer without email.`;
  //     } else {
  //       throw error;
  //     }
  //   } else {
  //     throw error;
  //   }
  // }

  // const extendedCustomer = await extendedCustomerService.createExtendedCustomers({
  //   ...customer.additional_data,
  // });

  // await link.create({
  //   [Modules.CUSTOMER]: {
  //     customer_id: createdCustomer.id,
  //   },
  //   [EXTENDED_CUSTOMER_MODULE]: {
  //     extended_customer_id: extendedCustomer.id,
  //   },
  // });

  return msg;
};

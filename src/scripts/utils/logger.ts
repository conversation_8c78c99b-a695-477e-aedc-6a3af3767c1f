import * as fs from 'fs/promises';
import * as path from 'path';

// Global log file paths - will be set by the main script
let DETAILED_LOG_PATH: string | null = null;
let ERROR_LOG_PATH: string | null = null;
let SUMMARY_LOG_PATH: string | null = null;

// Initialize logger with log file paths
export const initializeLogger = (detailedLogPath: string, errorLogPath: string, summaryLogPath: string) => {
  DETAILED_LOG_PATH = detailedLogPath;
  ERROR_LOG_PATH = errorLogPath;
  SUMMARY_LOG_PATH = summaryLogPath;
};

// Write to detailed log file
export const writeToDetailedLog = async (message: string) => {
  if (!DETAILED_LOG_PATH) return;
  
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  try {
    await fs.appendFile(DETAILED_LOG_PATH, logMessage);
  } catch (error) {
    console.error('Failed to write to detailed log:', error);
  }
};

// Write to summary log file
export const writeToSummaryLog = async (message: string) => {
  if (!SUMMARY_LOG_PATH) return;
  
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  try {
    await fs.appendFile(SUMMARY_LOG_PATH, logMessage);
  } catch (error) {
    console.error('Failed to write to summary log:', error);
  }
};

// Write to error log file
export const writeToErrorLog = async (message: string) => {
  if (!ERROR_LOG_PATH) return;
  
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  try {
    await fs.appendFile(ERROR_LOG_PATH, logMessage);
  } catch (error) {
    console.error('Failed to write to error log:', error);
  }
};

// Enhanced logging function for steps
export const logStepOperation = async (stepName: string, operation: string, details: any = {}, level: 'INFO' | 'ERROR' | 'DEBUG' | 'WARN' = 'INFO') => {
  const message = `[${stepName}] [${level}] ${operation} | ${JSON.stringify(details)}`;
  await writeToDetailedLog(message);
  
  // Also log to console for immediate feedback
  const timestamp = new Date().toISOString();
  const consoleMessage = `[${timestamp}] ${message}`;
  
  switch (level) {
    case 'ERROR':
      console.error(consoleMessage);
      break;
    case 'WARN':
      console.warn(consoleMessage);
      break;
    case 'DEBUG':
      console.debug(consoleMessage);
      break;
    default:
      console.log(consoleMessage);
  }
};

// Log step errors with full context
export const logStepError = async (stepName: string, operation: string, error: any, context: any = {}) => {
  const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);
  const message = `[${stepName}] [ERROR] ${operation} failed: ${errorMessage} | Context: ${JSON.stringify(context)}`;
  
  await writeToDetailedLog(message);
  await writeToErrorLog(`${stepName} - ${operation} - ${errorMessage} - Context: ${JSON.stringify(context)}`);
  
  // Also log to console
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] ${message}`);
};

// Check if logger is initialized
export const isLoggerInitialized = () => {
  return DETAILED_LOG_PATH !== null;
};

Customer ID,Error
********,"{""message"":""Customer with email: <EMAIL>, has_account: true, already exists."",""name"":""Error"",""stack"":""Error: Customer with email: <EMAIL>, has_account: true, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:35:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]"",""__isMedusaError"":true,""type"":""invalid_data"",""date"":""2025-06-01T19:04:27.507Z""}"

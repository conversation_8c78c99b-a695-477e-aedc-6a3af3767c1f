2025-06-01T19:33:15.659Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.644Z"} - Context: {"customerId":17458129,"customerIndex":26,"batchNumber":1,"subBatchNumber":6}
2025-06-01T19:33:15.693Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.690Z"} - Context: {"customerId":17455145,"customerIndex":35,"batchNumber":1,"subBatchNumber":7}
2025-06-01T19:33:15.724Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.716Z"} - Context: {"customerId":17455146,"customerIndex":36,"batchNumber":1,"subBatchNumber":8}
2025-06-01T19:33:15.765Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.756Z"} - Context: {"customerId":18060013,"customerIndex":43,"batchNumber":1,"subBatchNumber":9}
2025-06-01T19:33:15.767Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.763Z"} - Context: {"customerId":17422729,"customerIndex":44,"batchNumber":1,"subBatchNumber":9}
2025-06-01T19:33:15.796Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.787Z"} - Context: {"customerId":18108713,"customerIndex":47,"batchNumber":1,"subBatchNumber":10}
2025-06-01T19:33:15.832Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.821Z"} - Context: {"customerId":17509335,"customerIndex":54,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:33:15.833Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.827Z"} - Context: {"customerId":18092159,"customerIndex":51,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:33:15.833Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.828Z"} - Context: {"customerId":18464983,"customerIndex":53,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:33:15.870Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.867Z"} - Context: {"customerId":17511750,"customerIndex":60,"batchNumber":1,"subBatchNumber":12}
2025-06-01T19:33:15.908Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.897Z"} - Context: {"customerId":17455155,"customerIndex":61,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:33:15.912Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.904Z"} - Context: {"customerId":17510805,"customerIndex":65,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:33:15.913Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.908Z"} - Context: {"customerId":18278602,"customerIndex":62,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:33:15.973Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.956Z"} - Context: {"customerId":18078180,"customerIndex":69,"batchNumber":1,"subBatchNumber":14}
2025-06-01T19:33:15.987Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:15.983Z"} - Context: {"customerId":17463532,"customerIndex":70,"batchNumber":1,"subBatchNumber":14}
2025-06-01T19:33:16.031Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.024Z"} - Context: {"customerId":18078156,"customerIndex":74,"batchNumber":1,"subBatchNumber":15}
2025-06-01T19:33:16.033Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.030Z"} - Context: {"customerId":18480078,"customerIndex":75,"batchNumber":1,"subBatchNumber":15}
2025-06-01T19:33:16.065Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.057Z"} - Context: {"customerId":17512139,"customerIndex":80,"batchNumber":1,"subBatchNumber":16}
2025-06-01T19:33:16.066Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.056Z"} - Context: {"customerId":17511533,"customerIndex":78,"batchNumber":1,"subBatchNumber":16}
2025-06-01T19:33:16.100Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.097Z"} - Context: {"customerId":17511683,"customerIndex":85,"batchNumber":1,"subBatchNumber":17}
2025-06-01T19:33:16.133Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.127Z"} - Context: {"customerId":17511726,"customerIndex":86,"batchNumber":1,"subBatchNumber":18}
2025-06-01T19:33:16.133Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.128Z"} - Context: {"customerId":17508165,"customerIndex":88,"batchNumber":1,"subBatchNumber":18}
2025-06-01T19:33:16.135Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.132Z"} - Context: {"customerId":17438089,"customerIndex":87,"batchNumber":1,"subBatchNumber":18}
2025-06-01T19:33:16.168Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.161Z"} - Context: {"customerId":17508743,"customerIndex":93,"batchNumber":1,"subBatchNumber":19}
2025-06-01T19:33:16.200Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.192Z"} - Context: {"customerId":17511401,"customerIndex":97,"batchNumber":1,"subBatchNumber":20}
2025-06-01T19:33:16.201Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.198Z"} - Context: {"customerId":17511786,"customerIndex":98,"batchNumber":1,"subBatchNumber":20}
2025-06-01T19:33:16.327Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.321Z"} - Context: {"customerId":17509981,"customerIndex":108,"batchNumber":1,"subBatchNumber":22}
2025-06-01T19:33:16.329Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.326Z"} - Context: {"customerId":17509492,"customerIndex":106,"batchNumber":1,"subBatchNumber":22}
2025-06-01T19:33:16.360Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.354Z"} - Context: {"customerId":17416512,"customerIndex":114,"batchNumber":1,"subBatchNumber":23}
2025-06-01T19:33:16.389Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.386Z"} - Context: {"customerId":17462952,"customerIndex":116,"batchNumber":1,"subBatchNumber":24}
2025-06-01T19:33:16.418Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.414Z"} - Context: {"customerId":18428739,"customerIndex":124,"batchNumber":1,"subBatchNumber":25}
2025-06-01T19:33:16.447Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.443Z"} - Context: {"customerId":17506441,"customerIndex":127,"batchNumber":1,"subBatchNumber":26}
2025-06-01T19:33:16.447Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.441Z"} - Context: {"customerId":17451517,"customerIndex":128,"batchNumber":1,"subBatchNumber":26}
2025-06-01T19:33:16.447Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.442Z"} - Context: {"customerId":17509222,"customerIndex":129,"batchNumber":1,"subBatchNumber":26}
2025-06-01T19:33:16.476Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.471Z"} - Context: {"customerId":17467888,"customerIndex":134,"batchNumber":1,"subBatchNumber":27}
2025-06-01T19:33:16.506Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.499Z"} - Context: {"customerId":17511746,"customerIndex":138,"batchNumber":1,"subBatchNumber":28}
2025-06-01T19:33:16.508Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.504Z"} - Context: {"customerId":17511974,"customerIndex":140,"batchNumber":1,"subBatchNumber":28}
2025-06-01T19:33:16.548Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.545Z"} - Context: {"customerId":18428741,"customerIndex":142,"batchNumber":1,"subBatchNumber":29}
2025-06-01T19:33:16.594Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.582Z"} - Context: {"customerId":17508754,"customerIndex":147,"batchNumber":1,"subBatchNumber":30}
2025-06-01T19:33:16.595Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.588Z"} - Context: {"customerId":17434285,"customerIndex":150,"batchNumber":1,"subBatchNumber":30}
2025-06-01T19:33:16.693Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.688Z"} - Context: {"customerId":17455537,"customerIndex":157,"batchNumber":1,"subBatchNumber":32}
2025-06-01T19:33:16.694Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.690Z"} - Context: {"customerId":18104971,"customerIndex":156,"batchNumber":1,"subBatchNumber":32}
2025-06-01T19:33:16.723Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.717Z"} - Context: {"customerId":18274892,"customerIndex":163,"batchNumber":1,"subBatchNumber":33}
2025-06-01T19:33:16.724Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.721Z"} - Context: {"customerId":17494188,"customerIndex":162,"batchNumber":1,"subBatchNumber":33}
2025-06-01T19:33:16.752Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.748Z"} - Context: {"customerId":17443131,"customerIndex":169,"batchNumber":1,"subBatchNumber":34}
2025-06-01T19:33:16.752Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.749Z"} - Context: {"customerId":17414019,"customerIndex":166,"batchNumber":1,"subBatchNumber":34}
2025-06-01T19:33:16.780Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.772Z"} - Context: {"customerId":18298446,"customerIndex":174,"batchNumber":1,"subBatchNumber":35}
2025-06-01T19:33:16.783Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.781Z"} - Context: {"customerId":17478105,"customerIndex":175,"batchNumber":1,"subBatchNumber":35}
2025-06-01T19:33:16.811Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.803Z"} - Context: {"customerId":17459982,"customerIndex":180,"batchNumber":1,"subBatchNumber":36}
2025-06-01T19:33:16.814Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.812Z"} - Context: {"customerId":17491564,"customerIndex":176,"batchNumber":1,"subBatchNumber":36}
2025-06-01T19:33:16.848Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.836Z"} - Context: {"customerId":17495571,"customerIndex":181,"batchNumber":1,"subBatchNumber":37}
2025-06-01T19:33:16.850Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.845Z"} - Context: {"customerId":17495551,"customerIndex":183,"batchNumber":1,"subBatchNumber":37}
2025-06-01T19:33:16.895Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.881Z"} - Context: {"customerId":18309449,"customerIndex":186,"batchNumber":1,"subBatchNumber":38}
2025-06-01T19:33:16.896Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.888Z"} - Context: {"customerId":18460975,"customerIndex":190,"batchNumber":1,"subBatchNumber":38}
2025-06-01T19:33:16.898Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.895Z"} - Context: {"customerId":18453082,"customerIndex":189,"batchNumber":1,"subBatchNumber":38}
2025-06-01T19:33:16.956Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.950Z"} - Context: {"customerId":18418401,"customerIndex":200,"batchNumber":1,"subBatchNumber":40}
2025-06-01T19:33:16.959Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.954Z"} - Context: {"customerId":17453746,"customerIndex":198,"batchNumber":1,"subBatchNumber":40}
2025-06-01T19:33:16.987Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:16.982Z"} - Context: {"customerId":18383821,"customerIndex":204,"batchNumber":1,"subBatchNumber":41}
2025-06-01T19:33:17.038Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.034Z"} - Context: {"customerId":18265039,"customerIndex":215,"batchNumber":1,"subBatchNumber":43}
2025-06-01T19:33:17.068Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.062Z"} - Context: {"customerId":17416978,"customerIndex":218,"batchNumber":1,"subBatchNumber":44}
2025-06-01T19:33:17.068Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.060Z"} - Context: {"customerId":17484609,"customerIndex":217,"batchNumber":1,"subBatchNumber":44}
2025-06-01T19:33:17.092Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.086Z"} - Context: {"customerId":18513306,"customerIndex":224,"batchNumber":1,"subBatchNumber":45}
2025-06-01T19:33:17.122Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.114Z"} - Context: {"customerId":18284726,"customerIndex":229,"batchNumber":1,"subBatchNumber":46}
2025-06-01T19:33:17.124Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.121Z"} - Context: {"customerId":17492945,"customerIndex":230,"batchNumber":1,"subBatchNumber":46}
2025-06-01T19:33:17.203Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.197Z"} - Context: {"customerId":17488583,"customerIndex":240,"batchNumber":1,"subBatchNumber":48}
2025-06-01T19:33:17.204Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.199Z"} - Context: {"customerId":18515033,"customerIndex":236,"batchNumber":1,"subBatchNumber":48}
2025-06-01T19:33:17.205Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.200Z"} - Context: {"customerId":18345053,"customerIndex":239,"batchNumber":1,"subBatchNumber":48}
2025-06-01T19:33:17.317Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.315Z"} - Context: {"customerId":18411030,"customerIndex":244,"batchNumber":1,"subBatchNumber":49}
2025-06-01T19:33:17.339Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.330Z"} - Context: {"customerId":18322192,"customerIndex":246,"batchNumber":1,"subBatchNumber":50}
2025-06-01T19:33:17.342Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.335Z"} - Context: {"customerId":17452160,"customerIndex":249,"batchNumber":1,"subBatchNumber":50}
2025-06-01T19:33:17.344Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.340Z"} - Context: {"customerId":17505511,"customerIndex":250,"batchNumber":1,"subBatchNumber":50}
2025-06-01T19:33:17.374Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.367Z"} - Context: {"customerId":17490508,"customerIndex":255,"batchNumber":1,"subBatchNumber":51}
2025-06-01T19:33:17.376Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.371Z"} - Context: {"customerId":18337281,"customerIndex":254,"batchNumber":1,"subBatchNumber":51}
2025-06-01T19:33:17.400Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.398Z"} - Context: {"customerId":18530254,"customerIndex":260,"batchNumber":1,"subBatchNumber":52}
2025-06-01T19:33:17.422Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.417Z"} - Context: {"customerId":17442564,"customerIndex":265,"batchNumber":1,"subBatchNumber":53}
2025-06-01T19:33:17.426Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.422Z"} - Context: {"customerId":17424133,"customerIndex":263,"batchNumber":1,"subBatchNumber":53}
2025-06-01T19:33:17.450Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.445Z"} - Context: {"customerId":18466131,"customerIndex":269,"batchNumber":1,"subBatchNumber":54}
2025-06-01T19:33:17.451Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.449Z"} - Context: {"customerId":18412932,"customerIndex":267,"batchNumber":1,"subBatchNumber":54}
2025-06-01T19:33:17.478Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.473Z"} - Context: {"customerId":18300497,"customerIndex":273,"batchNumber":1,"subBatchNumber":55}
2025-06-01T19:33:17.503Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.498Z"} - Context: {"customerId":18380815,"customerIndex":278,"batchNumber":1,"subBatchNumber":56}
2025-06-01T19:33:17.529Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.520Z"} - Context: {"customerId":17497266,"customerIndex":281,"batchNumber":1,"subBatchNumber":57}
2025-06-01T19:33:17.532Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.528Z"} - Context: {"customerId":17469616,"customerIndex":282,"batchNumber":1,"subBatchNumber":57}
2025-06-01T19:33:17.555Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.550Z"} - Context: {"customerId":18070474,"customerIndex":290,"batchNumber":1,"subBatchNumber":58}
2025-06-01T19:33:17.555Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.549Z"} - Context: {"customerId":18408081,"customerIndex":288,"batchNumber":1,"subBatchNumber":58}
2025-06-01T19:33:17.581Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.578Z"} - Context: {"customerId":17493471,"customerIndex":294,"batchNumber":1,"subBatchNumber":59}
2025-06-01T19:33:17.607Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.602Z"} - Context: {"customerId":18526035,"customerIndex":297,"batchNumber":1,"subBatchNumber":60}
2025-06-01T19:33:17.608Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.603Z"} - Context: {"customerId":18505421,"customerIndex":296,"batchNumber":1,"subBatchNumber":60}
2025-06-01T19:33:17.608Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.601Z"} - Context: {"customerId":18502399,"customerIndex":299,"batchNumber":1,"subBatchNumber":60}
2025-06-01T19:33:17.608Z - Customer Processing - {"message":"Cannot create multiple links between 'customer' and 'extended_customer'","name":"Error","stack":"Error: Cannot create multiple links between 'customer' and 'extended_customer'\n    at Link.create (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:514:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.exports.createRemoteLinkStep.async.container.container (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/core-flows/src/common/steps/create-remote-links.ts:31:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n    at async TransactionOrchestrator.executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:882:7)\n    at async executeNext (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1253:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> create-remote-links (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T19:33:17.604Z"} - Context: {"customerId":18306238,"customerIndex":298,"batchNumber":1,"subBatchNumber":60}

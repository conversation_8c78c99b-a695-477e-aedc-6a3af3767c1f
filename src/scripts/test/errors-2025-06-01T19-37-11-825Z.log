2025-06-01T19:37:18.292Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17508455,"customerIndex":1,"batchNumber":1,"subBatchNumber":1}
2025-06-01T19:37:18.298Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511365,"customerIndex":2,"batchNumber":1,"subBatchNumber":1}
2025-06-01T19:37:18.326Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17445898,"customerIndex":5,"batchNumber":1,"subBatchNumber":1}
2025-06-01T19:37:18.326Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18055094,"customerIndex":4,"batchNumber":1,"subBatchNumber":1}
2025-06-01T19:37:18.326Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17460856,"customerIndex":3,"batchNumber":1,"subBatchNumber":1}
2025-06-01T19:37:18.400Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18411690,"customerIndex":7,"batchNumber":1,"subBatchNumber":2}
2025-06-01T19:37:18.400Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18092223,"customerIndex":9,"batchNumber":1,"subBatchNumber":2}
2025-06-01T19:37:18.401Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18411691,"customerIndex":8,"batchNumber":1,"subBatchNumber":2}
2025-06-01T19:37:18.402Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18274266,"customerIndex":10,"batchNumber":1,"subBatchNumber":2}
2025-06-01T19:37:18.402Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17434638,"customerIndex":6,"batchNumber":1,"subBatchNumber":2}
2025-06-01T19:37:18.452Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17508760,"customerIndex":13,"batchNumber":1,"subBatchNumber":3}
2025-06-01T19:37:18.452Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17508623,"customerIndex":11,"batchNumber":1,"subBatchNumber":3}
2025-06-01T19:37:18.457Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17508707,"customerIndex":12,"batchNumber":1,"subBatchNumber":3}
2025-06-01T19:37:18.458Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509980,"customerIndex":15,"batchNumber":1,"subBatchNumber":3}
2025-06-01T19:37:18.459Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509049,"customerIndex":14,"batchNumber":1,"subBatchNumber":3}
2025-06-01T19:37:18.509Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18078094,"customerIndex":17,"batchNumber":1,"subBatchNumber":4}
2025-06-01T19:37:18.514Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18516064,"customerIndex":20,"batchNumber":1,"subBatchNumber":4}
2025-06-01T19:37:18.516Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17468100,"customerIndex":19,"batchNumber":1,"subBatchNumber":4}
2025-06-01T19:37:18.516Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18276563,"customerIndex":18,"batchNumber":1,"subBatchNumber":4}
2025-06-01T19:37:18.516Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511406,"customerIndex":16,"batchNumber":1,"subBatchNumber":4}
2025-06-01T19:37:18.580Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17504013,"customerIndex":25,"batchNumber":1,"subBatchNumber":5}
2025-06-01T19:37:18.582Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17448857,"customerIndex":23,"batchNumber":1,"subBatchNumber":5}
2025-06-01T19:37:18.583Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511936,"customerIndex":22,"batchNumber":1,"subBatchNumber":5}
2025-06-01T19:37:18.583Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510275,"customerIndex":21,"batchNumber":1,"subBatchNumber":5}
2025-06-01T19:37:18.584Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510525,"customerIndex":24,"batchNumber":1,"subBatchNumber":5}
2025-06-01T19:37:18.639Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17458129,"customerIndex":26,"batchNumber":1,"subBatchNumber":6}
2025-06-01T19:37:18.643Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511697,"customerIndex":27,"batchNumber":1,"subBatchNumber":6}
2025-06-01T19:37:18.643Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17512149,"customerIndex":28,"batchNumber":1,"subBatchNumber":6}
2025-06-01T19:37:18.643Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17456923,"customerIndex":29,"batchNumber":1,"subBatchNumber":6}
2025-06-01T19:37:18.646Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18272806,"customerIndex":30,"batchNumber":1,"subBatchNumber":6}
2025-06-01T19:37:18.696Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17419984,"customerIndex":33,"batchNumber":1,"subBatchNumber":7}
2025-06-01T19:37:18.696Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511347,"customerIndex":31,"batchNumber":1,"subBatchNumber":7}
2025-06-01T19:37:18.697Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17453898,"customerIndex":32,"batchNumber":1,"subBatchNumber":7}
2025-06-01T19:37:18.705Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510335,"customerIndex":34,"batchNumber":1,"subBatchNumber":7}
2025-06-01T19:37:18.705Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17455145,"customerIndex":35,"batchNumber":1,"subBatchNumber":7}
2025-06-01T19:37:18.757Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17500902,"customerIndex":37,"batchNumber":1,"subBatchNumber":8}
2025-06-01T19:37:18.757Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18428737,"customerIndex":38,"batchNumber":1,"subBatchNumber":8}
2025-06-01T19:37:18.823Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18105199,"customerIndex":39,"batchNumber":1,"subBatchNumber":8}
2025-06-01T19:37:18.841Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17453902,"customerIndex":40,"batchNumber":1,"subBatchNumber":8}
2025-06-01T19:37:18.843Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17455146,"customerIndex":36,"batchNumber":1,"subBatchNumber":8}
2025-06-01T19:37:18.900Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17422729,"customerIndex":44,"batchNumber":1,"subBatchNumber":9}
2025-06-01T19:37:18.900Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18060013,"customerIndex":43,"batchNumber":1,"subBatchNumber":9}
2025-06-01T19:37:18.903Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509606,"customerIndex":41,"batchNumber":1,"subBatchNumber":9}
2025-06-01T19:37:18.906Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17464034,"customerIndex":45,"batchNumber":1,"subBatchNumber":9}
2025-06-01T19:37:18.907Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509614,"customerIndex":42,"batchNumber":1,"subBatchNumber":9}
2025-06-01T19:37:18.946Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510850,"customerIndex":46,"batchNumber":1,"subBatchNumber":10}
2025-06-01T19:37:18.952Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18108713,"customerIndex":47,"batchNumber":1,"subBatchNumber":10}
2025-06-01T19:37:18.952Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18124366,"customerIndex":49,"batchNumber":1,"subBatchNumber":10}
2025-06-01T19:37:18.955Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509393,"customerIndex":48,"batchNumber":1,"subBatchNumber":10}
2025-06-01T19:37:18.955Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509832,"customerIndex":50,"batchNumber":1,"subBatchNumber":10}
2025-06-01T19:37:19.000Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18411692,"customerIndex":52,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:37:19.007Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18092159,"customerIndex":51,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:37:19.007Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18464983,"customerIndex":53,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:37:19.009Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509335,"customerIndex":54,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:37:19.010Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510349,"customerIndex":55,"batchNumber":1,"subBatchNumber":11}
2025-06-01T19:37:19.053Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510387,"customerIndex":58,"batchNumber":1,"subBatchNumber":12}
2025-06-01T19:37:19.054Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510703,"customerIndex":59,"batchNumber":1,"subBatchNumber":12}
2025-06-01T19:37:19.054Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17508763,"customerIndex":56,"batchNumber":1,"subBatchNumber":12}
2025-06-01T19:37:19.057Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511750,"customerIndex":60,"batchNumber":1,"subBatchNumber":12}
2025-06-01T19:37:19.057Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510379,"customerIndex":57,"batchNumber":1,"subBatchNumber":12}
2025-06-01T19:37:19.108Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18281393,"customerIndex":63,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:37:19.108Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17455155,"customerIndex":61,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:37:19.108Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18278602,"customerIndex":62,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:37:19.108Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18446464,"customerIndex":64,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:37:19.108Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17510805,"customerIndex":65,"batchNumber":1,"subBatchNumber":13}
2025-06-01T19:37:19.166Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18274269,"customerIndex":66,"batchNumber":1,"subBatchNumber":14}
2025-06-01T19:37:19.167Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17463532,"customerIndex":70,"batchNumber":1,"subBatchNumber":14}
2025-06-01T19:37:19.167Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17455796,"customerIndex":68,"batchNumber":1,"subBatchNumber":14}
2025-06-01T19:37:19.169Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18078180,"customerIndex":69,"batchNumber":1,"subBatchNumber":14}
2025-06-01T19:37:19.169Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509035,"customerIndex":67,"batchNumber":1,"subBatchNumber":14}
2025-06-01T19:37:19.215Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18411693,"customerIndex":71,"batchNumber":1,"subBatchNumber":15}
2025-06-01T19:37:19.216Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18480078,"customerIndex":75,"batchNumber":1,"subBatchNumber":15}
2025-06-01T19:37:19.223Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18078156,"customerIndex":74,"batchNumber":1,"subBatchNumber":15}
2025-06-01T19:37:19.223Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511481,"customerIndex":72,"batchNumber":1,"subBatchNumber":15}
2025-06-01T19:37:19.224Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18137054,"customerIndex":73,"batchNumber":1,"subBatchNumber":15}
2025-06-01T19:37:19.264Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18188843,"customerIndex":79,"batchNumber":1,"subBatchNumber":16}
2025-06-01T19:37:19.265Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17436880,"customerIndex":76,"batchNumber":1,"subBatchNumber":16}
2025-06-01T19:37:19.265Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509994,"customerIndex":77,"batchNumber":1,"subBatchNumber":16}
2025-06-01T19:37:19.272Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17512139,"customerIndex":80,"batchNumber":1,"subBatchNumber":16}
2025-06-01T19:37:19.272Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511533,"customerIndex":78,"batchNumber":1,"subBatchNumber":16}
2025-06-01T19:37:19.331Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509644,"customerIndex":83,"batchNumber":1,"subBatchNumber":17}
2025-06-01T19:37:19.332Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511289,"customerIndex":84,"batchNumber":1,"subBatchNumber":17}
2025-06-01T19:37:19.332Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17511683,"customerIndex":85,"batchNumber":1,"subBatchNumber":17}
2025-06-01T19:37:19.332Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":18274270,"customerIndex":81,"batchNumber":1,"subBatchNumber":17}
2025-06-01T19:37:19.333Z - Customer Processing - {"message":"Cannot convert undefined or null to object","name":"TypeError","stack":"TypeError: Cannot convert undefined or null to object\n    at Function.keys (<anonymous>)\n    at Link.getLinkDataConfig (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:385:31)\n    at Link.getLinkModuleOrThrow (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:362:12)\n    at Link.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/modules-sdk/src/link.ts:541:28)\n    at Proxy.dismiss (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/local-workflow.ts:131:27)\n    at Object.<anonymous> (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-extended-customer.ts:65:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-extended-customer (invoke)]"} - Context: {"customerId":17509534,"customerIndex":82,"batchNumber":1,"subBatchNumber":17}

import { exec } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as readline from 'readline';
import { CustomerAddressDTO, CustomerDTO, ExecArgs, ICustomerModuleService, RemoteQueryFunction } from '@medusajs/framework/types';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { createItemInDB } from '../utils/customers';
import { Link } from '@medusajs/framework/modules-sdk';
import ExtendedCustomerService from '../../modules/extended/customer/service';

// Create unique log files for each script run
const SCRIPT_RUN_ID = new Date().toISOString().replace(/[:.]/g, '-');
const DETAILED_LOG = path.join(__dirname, `detailed-operations-${SCRIPT_RUN_ID}.log`);
const ERROR_LOG = path.join(__dirname, `errors-${SCRIPT_RUN_ID}.log`);
const SUMMARY_LOG = path.join(__dirname, `summary-${SCRIPT_RUN_ID}.log`);

let TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kYpsFwj_dqywOhUnw1ZY0V9q5A4bDvpdra2KN2BPgyw'; // Replace with your token

export type CustomerDTOWithModifiedAddress<T extends keyof CustomerAddressDTO> = Omit<CustomerDTO, "addresses"> & {
  addresses: Omit<CustomerAddressDTO, T>[];
};

interface servicePack {
  customerService: ICustomerModuleService;
  extendedCustomerService: ExtendedCustomerService;
  link: Link;
  query: Omit<RemoteQueryFunction, symbol>
}

// 👇 API Customer Data Interface
export interface APICustomerData {
  mobile: string;
  id: number;
  uuid: string;
  city: string;
  email: string;
  buyer_id: number;
  notes: string | null;
  buyer_uuid: string;
  state: string;
  pin: string;
  address_line: string;
  name: string;
  type: string;
  created_at: string;
  credit_balance: number | null;
  expirable_balance: number | null;
  tags: string[];
  orders_total_cost: number;
  orders: number;
  last_order_created_at: string;
}

// 👇 Enhanced logging functions with separate files
const writeToDetailedLog = async (message: string) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  await fs.appendFile(DETAILED_LOG, logMessage);
};

const writeToSummaryLog = async (message: string) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  await fs.appendFile(SUMMARY_LOG, logMessage);
};

const appendLog = async (message: string, level: 'INFO' | 'ERROR' | 'DEBUG' | 'WARN' = 'INFO') => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Write to detailed log file
  await writeToDetailedLog(`[${level}] ${message}`);

  // Also log to console with appropriate level
  switch (level) {
    case 'ERROR':
      console.error(logMessage);
      break;
    case 'WARN':
      console.warn(logMessage);
      break;
    case 'DEBUG':
      console.debug(logMessage);
      break;
    default:
      console.log(logMessage);
  }
};

const logOperation = async (operation: string, details: any = {}) => {
  const message = `OPERATION: ${operation} | Details: ${JSON.stringify(details)}`;
  await appendLog(message, 'INFO');
  await writeToDetailedLog(`OPERATION: ${operation} | ${JSON.stringify(details, null, 2)}`);
};

const logError = async (operation: string, error: any, context: any = {}) => {
  const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);
  const message = `ERROR in ${operation}: ${errorMessage} | Context: ${JSON.stringify(context)}`;
  await appendLog(message, 'ERROR');

  // Also log to error file
  const log = `${new Date().toISOString()} - ${operation} - ${errorMessage} - Context: ${JSON.stringify(context)}\n`;
  await fs.appendFile(ERROR_LOG, log);
};

// 👇 Validate function: maps API data to CustomerDTO
const validateAPICustomer = async (apiData: APICustomerData): Promise<CustomerDTOWithModifiedAddress<'id'> & { additional_data: any }> => {
  await logOperation('validateAPICustomer', { customerId: apiData.id, email: apiData.email, mobile: apiData.mobile });

  const errors: string[] = [];

  if (!apiData.id) errors.push('Missing Customer ID');
  if (!apiData.mobile) errors.push('Missing Phone Number');

  const totalOrders = apiData.orders || 0;
  const totalSales = apiData.orders_total_cost || 0;

  const loyaltyPerpetual = Number(parseFloat((apiData.credit_balance || 0).toString()).toFixed(3));
  const loyaltyExpirable = Number(parseFloat((apiData.expirable_balance || 0).toString()).toFixed(3));

  const tags = apiData.tags || [];

  await appendLog(`Validation for Customer ID ${apiData.id}: totalOrders=${totalOrders}, totalSales=${totalSales}, loyaltyPerpetual=${loyaltyPerpetual}, loyaltyExpirable=${loyaltyExpirable}`, 'DEBUG');

  if (errors.length > 0) {
    await appendLog(`Validation failed for Customer ID ${apiData.id}: ${errors.join('; ')}`, 'ERROR');
    throw new Error(errors.join('; '));
  }

  // Map to DTO
  const customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any } = {
    id: apiData.id.toString(),
    email: apiData.email,
    has_account: false,
    default_billing_address_id: null,
    default_shipping_address_id: null,
    company_name: null,
    first_name: apiData.name ? apiData.name.split(' ')[0] || null : null,
    last_name: apiData.name ? apiData.name.split(' ').slice(1).join(' ') || null : null,
    addresses: apiData.city ? [{
      address_1: apiData.city,
      city: apiData.city,
      country_code: 'IN',
      postal_code: apiData.pin || '',
      province: apiData.state || '',
      customer_id: apiData.id.toString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_default_shipping: true,
      is_default_billing: true,
    }] : [],
    phone: apiData.mobile || null,
    groups: [],
    additional_data: {
      totalOrders,
      totalSales,
      non_expirable_loyalty_points: loyaltyPerpetual,
      expirable_loyalty_points: loyaltyExpirable,
      notes: apiData.notes,
      last_order_created_at: apiData.last_order_created_at,
      type: apiData.type,
    },
    metadata: {},
    created_by: 'api-migration-script',
    deleted_at: null,
    created_at: apiData.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  await appendLog(`Successfully validated Customer ID ${apiData.id}: mapped to DTO with ${customer.addresses.length} addresses`, 'DEBUG');
  await logOperation('validateAPICustomer_completed', {
    customerId: customer.id,
    hasAddresses: customer.addresses.length > 0,
    additionalDataKeys: Object.keys(customer.additional_data)
  });

  return customer;
};

// === Terminal Input ===
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});
const prompt = (query: string) => new Promise<string>((resolve) => rl.question(query, resolve));

// === Base CURL Command ===
const BASE_CURL_COMMAND = (page: number) => `
curl -s 'https://api-enterprise.mydukaan.io/api/store/seller/store-buyer/?ordering=-orders_total_cost&page=${page}&page_size=30' \
-H 'Authorization: Bearer ${TOKEN}' \
-H 'User-Agent: Mozilla/5.0' \
-H 'Accept: application/json' \
-H 'x-Mode: seller-web' \
-H 'x-dukaan-store-id: 202299269' \
--compressed
`;

// === Error Logging (moved to enhanced logging functions above) ===

// === cURL Execution ===
async function executeCURL(page: number): Promise<any[]> {
  await logOperation('executeCURL_start', { page });
  await appendLog(`Starting cURL execution for page ${page}`, 'DEBUG');

  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    exec(BASE_CURL_COMMAND(page), async (error, stdout, stderr) => {
      const executionTime = Date.now() - startTime;

      if (error) {
        const errorMsg = `Error on page ${page}: ${error.message}`;
        await appendLog(`cURL execution failed for page ${page} after ${executionTime}ms: ${error.message}`, 'ERROR');
        await logError('executeCURL', error, { page, executionTime });
        reject(errorMsg);
        return;
      }

      if (stderr && !stderr.trim().startsWith('% Total')) {
        const errorMsg = `Stderr on page ${page}: ${stderr}`;
        await appendLog(`cURL stderr for page ${page}: ${stderr}`, 'WARN');
        reject(errorMsg);
        return;
      }

      try {
        await appendLog(`Parsing JSON response for page ${page} (execution time: ${executionTime}ms)`, 'DEBUG');
        const json = JSON.parse(stdout);

        if (json.detail && json.detail.includes('Invalid token')) {
          const errorMsg = `Token expired on page ${page}`;
          await appendLog(errorMsg, 'ERROR');
          reject({ type: '401', message: errorMsg });
          return;
        }

        if (Array.isArray(json.results)) {
          await appendLog(`Successfully fetched ${json.results.length} results from page ${page} in ${executionTime}ms`, 'INFO');
          await logOperation('executeCURL_success', { page, resultCount: json.results.length, executionTime });
          resolve(json.results);
        } else {
          await appendLog(`No results array found in response for page ${page}`, 'WARN');
          await logOperation('executeCURL_empty', { page, executionTime });
          resolve([]); // Empty results
        }
      } catch (err) {
        const errorMsg = `Failed to parse JSON on page ${page}: ${err}`;
        await appendLog(`JSON parsing failed for page ${page}: ${err}`, 'ERROR');
        await logError('executeCURL_parse', err, { page, executionTime });
        reject(errorMsg);
      }
    });
  });
}

// === Database Migration Setup ===
let servicePack: servicePack;
let container: any;

// === Batch Fetch Logic ===
export default async function fetchAllPages({ container: containerArg }: ExecArgs) {
  // Initialize log files
  await writeToDetailedLog('='.repeat(80));
  await writeToDetailedLog(`SCRIPT EXECUTION STARTED - ${new Date().toISOString()}`);
  await writeToDetailedLog('='.repeat(80));
  await writeToSummaryLog(`Migration script started at ${new Date().toISOString()}`);

  await logOperation('fetchAllPages_start', {});
  await appendLog('Starting batch fetch process', 'INFO');

  // Set up services
  await appendLog('Resolving services from container', 'DEBUG');
  const customerService = containerArg.resolve('customer');
  const extendedCustomerService = containerArg.resolve('extended_customer');
  const linkService = containerArg.resolve('link');
  const query = containerArg.resolve(ContainerRegistrationKeys.QUERY);

  servicePack = {
    customerService,
    extendedCustomerService,
    link: linkService,
    query,
  };
  container = containerArg;

  await appendLog('Services resolved successfully', 'DEBUG');
  await logOperation('services_resolved', {
    hasCustomerService: !!customerService,
    hasExtendedCustomerService: !!extendedCustomerService,
    hasLinkService: !!linkService,
    hasQuery: !!query
  });

  const batchSize = 10;
  await appendLog(`Configured batch size: ${batchSize}`, 'INFO');

  // Ask user for starting page
  let inputPage = await prompt('Enter the starting page number (default 1): ');
  inputPage = inputPage.trim();
  let page = 1;
  if (inputPage) {
    const parsed = parseInt(inputPage, 10);
    if (isNaN(parsed) || parsed < 1) {
      await appendLog(`Invalid input "${inputPage}", starting from page 1.`, 'WARN');
      console.log(`Invalid input "${inputPage}", starting from page 1.`);
    } else {
      page = parsed;
      await appendLog(`Starting from user-specified page: ${page}`, 'INFO');
    }
  }

  await logOperation('batch_fetch_start', { startingPage: page, batchSize });
  console.log('🚀 Starting batch fetch...');
  await appendLog(`Starting batch fetch from page ${page} with batch size ${batchSize}`, 'INFO');
  await writeToSummaryLog(`Starting migration from page ${page} with batch size ${batchSize}`);

  let totalProcessedCustomers = 0;
  let totalBatches = 0;
  const migrationStartTime = Date.now();

  while (true) {
    totalBatches++;
    const batchStartTime = Date.now();

    console.log(`🔎 Preparing batch starting from page ${page}...`);
    await appendLog(`Preparing batch ${totalBatches} starting from page ${page}`, 'INFO');
    await writeToSummaryLog(`Starting batch ${totalBatches} from page ${page}`);

    const batchPages = Array.from({ length: batchSize }, (_, i) => page + i);
    console.log(`➡️ Batch pages:`, batchPages);
    await logOperation('batch_pages_prepared', { batchNumber: totalBatches, pages: batchPages });

    if (batchPages.length === 0) {
      console.log('✅ No more pages to fetch.');
      await appendLog('No more pages to fetch - ending batch processing', 'INFO');
      break;
    }

    await appendLog(`Executing cURL requests for ${batchPages.length} pages`, 'DEBUG');
    const promises = batchPages.map((p) =>
      executeCURL(p).catch(async (err) => {
        console.error(`❌ Error in page ${p}:`, err);
        await appendLog(`Error in page ${p}: ${err}`, 'ERROR');
        return [];
      })
    );

    const results = await Promise.all(promises);
    const flatResults = results.flat();
    const batchFetchTime = Date.now() - batchStartTime;

    await logOperation('batch_fetch_completed', {
      batchNumber: totalBatches,
      pagesRequested: batchPages.length,
      totalResults: flatResults.length,
      fetchTime: batchFetchTime
    });

    if (flatResults.length === 0) {
      console.log('✅ No more results. Fetch complete.');
      await appendLog('No more results found - fetch complete', 'INFO');
      await writeToSummaryLog(`No more results found - fetch complete after ${totalBatches} batches`);
      break;
    }

    console.log(`📦 Processing batch (${flatResults.length} records)...`);
    await appendLog(`Processing batch ${totalBatches} with ${flatResults.length} records (fetch time: ${batchFetchTime}ms)`, 'INFO');

    // Process each customer in the batch
    const CUSTOMER_BATCH_SIZE = 5; // Smaller batch for database operations
    await appendLog(`Processing ${flatResults.length} customers in sub-batches of ${CUSTOMER_BATCH_SIZE}`, 'DEBUG');

    let batchProcessedCustomers = 0;
    const customerProcessingStartTime = Date.now();

    for (let i = 0; i < flatResults.length; i += CUSTOMER_BATCH_SIZE) {
      const customerBatch = flatResults.slice(i, i + CUSTOMER_BATCH_SIZE);
      const subBatchNumber = Math.floor(i / CUSTOMER_BATCH_SIZE) + 1;

      await appendLog(`Processing customer sub-batch ${subBatchNumber} (${customerBatch.length} customers)`, 'DEBUG');
      await logOperation('customer_sub_batch_start', {
        batchNumber: totalBatches,
        subBatchNumber,
        customerCount: customerBatch.length
      });

      await Promise.all(
        customerBatch.map(async (apiCustomer: APICustomerData, index) => {
          const customerIndex = i + index + 1;
          const globalCustomerIndex = totalProcessedCustomers + customerIndex;

          try {
            await appendLog(`Processing customer ${globalCustomerIndex} (ID: ${apiCustomer.id})`, 'DEBUG');
            const validatedCustomer = await validateAPICustomer(apiCustomer);
            const msg = await createItemInDB(validatedCustomer, servicePack, container);
            const successMsg = `Customer ${globalCustomerIndex}: ${msg}`;
            console.log(successMsg);
            await appendLog(successMsg);
            await logOperation('customer_processed_success', {
              customerId: apiCustomer.id,
              globalIndex: globalCustomerIndex,
              message: msg
            });
            batchProcessedCustomers++;
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
            const failMsg = `Customer ${globalCustomerIndex}: ERROR - ${errorMsg}`;
            console.error(failMsg);
            await appendLog(failMsg, 'ERROR');
            await logError('Customer Processing', error, {
              customerId: apiCustomer.id,
              customerIndex: globalCustomerIndex,
              batchNumber: totalBatches,
              subBatchNumber
            });
          }
        })
      );

      await logOperation('customer_sub_batch_completed', {
        batchNumber: totalBatches,
        subBatchNumber,
        processedCount: customerBatch.length
      });
    }

    totalProcessedCustomers += batchProcessedCustomers;
    const customerProcessingTime = Date.now() - customerProcessingStartTime;
    const totalBatchTime = Date.now() - batchStartTime;

    await appendLog(`Batch ${totalBatches} completed: processed ${batchProcessedCustomers} customers in ${customerProcessingTime}ms (total batch time: ${totalBatchTime}ms)`, 'INFO');
    await writeToSummaryLog(`Batch ${totalBatches} completed: ${batchProcessedCustomers} customers processed in ${totalBatchTime}ms`);
    await logOperation('batch_completed', {
      batchNumber: totalBatches,
      customersProcessed: batchProcessedCustomers,
      totalCustomersProcessed: totalProcessedCustomers,
      processingTime: customerProcessingTime,
      totalBatchTime
    });

    page += batchSize;
  }

  const migrationEndTime = Date.now();
  const totalMigrationTime = migrationEndTime - migrationStartTime;

  console.log(`🎉 Migration complete.`);
  await appendLog(`Migration complete. Total customers processed: ${totalProcessedCustomers} across ${totalBatches} batches in ${totalMigrationTime}ms`, 'INFO');
  await writeToSummaryLog(`Migration completed: ${totalProcessedCustomers} customers processed across ${totalBatches} batches in ${totalMigrationTime}ms`);
  await writeToDetailedLog('='.repeat(80));
  await writeToDetailedLog(`SCRIPT EXECUTION COMPLETED - ${new Date().toISOString()}`);
  await writeToDetailedLog(`Total execution time: ${totalMigrationTime}ms`);
  await writeToDetailedLog('='.repeat(80));

  await logOperation('migration_completed', {
    totalCustomersProcessed: totalProcessedCustomers,
    totalBatches,
    finalPage: page,
    totalMigrationTime
  });

  console.log(`📁 Log files created:`);
  console.log(`   Detailed operations: ${DETAILED_LOG}`);
  console.log(`   Errors: ${ERROR_LOG}`);
  console.log(`   Summary: ${SUMMARY_LOG}`);

  rl.close(); // Close readline interface when done
}

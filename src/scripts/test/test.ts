import { exec } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as readline from 'readline';
import { CustomerAddressDTO, CustomerDTO, ExecArgs, ICustomerModuleService, RemoteQueryFunction } from '@medusajs/framework/types';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { createItemInDB } from '../utils/customers';
import { Link } from '@medusajs/framework/modules-sdk';
import ExtendedCustomerService from '../../modules/extended/customer/service';

const ERROR_LOG = path.join(__dirname, 'errors.log');
let TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kYpsFwj_dqywOhUnw1ZY0V9q5A4bDvpdra2KN2BPgyw'; // Replace with your token

export type CustomerDTOWithModifiedAddress<T extends keyof CustomerAddressDTO> = Omit<CustomerDTO, "addresses"> & {
  addresses: Omit<CustomerAddressDTO, T>[];
};

interface servicePack {
  customerService: ICustomerModuleService;
  extendedCustomerService: ExtendedCustomerService;
  link: Link;
  query: Omit<RemoteQueryFunction, symbol>
}

// 👇 API Customer Data Interface
export interface APICustomerData {
  mobile: string;
  id: number;
  uuid: string;
  city: string;
  email: string;
  buyer_id: number;
  notes: string | null;
  buyer_uuid: string;
  state: string;
  pin: string;
  address_line: string;
  name: string;
  type: string;
  created_at: string;
  credit_balance: number | null;
  expirable_balance: number | null;
  tags: string[];
  orders_total_cost: number;
  orders: number;
  last_order_created_at: string;
}

// 👇 Enhanced logging functions
const appendLog = (message: string, level: 'INFO' | 'ERROR' | 'DEBUG' | 'WARN' = 'INFO') => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  const logPath = path.join(__dirname, 'migration-log.txt');
  require('fs').appendFileSync(logPath, logMessage + '\n');

  // Also log to console with appropriate level
  switch (level) {
    case 'ERROR':
      console.error(logMessage);
      break;
    case 'WARN':
      console.warn(logMessage);
      break;
    case 'DEBUG':
      console.debug(logMessage);
      break;
    default:
      console.log(logMessage);
  }
};

const logOperation = (operation: string, details: any = {}) => {
  const message = `OPERATION: ${operation} | Details: ${JSON.stringify(details)}`;
  appendLog(message, 'INFO');
};

const logError = async (operation: string, error: any, context: any = {}) => {
  const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);
  const message = `ERROR in ${operation}: ${errorMessage} | Context: ${JSON.stringify(context)}`;
  appendLog(message, 'ERROR');

  // Also log to error file
  const log = `${new Date().toISOString()} - ${operation} - ${errorMessage} - Context: ${JSON.stringify(context)}\n`;
  await fs.appendFile(ERROR_LOG, log);
};

// 👇 Validate function: maps API data to CustomerDTO
const validateAPICustomer = async (apiData: APICustomerData): Promise<CustomerDTOWithModifiedAddress<'id'> & { additional_data: any }> => {
  logOperation('validateAPICustomer', { customerId: apiData.id, email: apiData.email, mobile: apiData.mobile });

  const errors: string[] = [];

  if (!apiData.id) errors.push('Missing Customer ID');
  if (!apiData.mobile) errors.push('Missing Phone Number');

  const totalOrders = apiData.orders || 0;
  const totalSales = apiData.orders_total_cost || 0;

  const loyaltyPerpetual = Number(parseFloat((apiData.credit_balance || 0).toString()).toFixed(3));
  const loyaltyExpirable = Number(parseFloat((apiData.expirable_balance || 0).toString()).toFixed(3));

  const tags = apiData.tags || [];

  appendLog(`Validation for Customer ID ${apiData.id}: totalOrders=${totalOrders}, totalSales=${totalSales}, loyaltyPerpetual=${loyaltyPerpetual}, loyaltyExpirable=${loyaltyExpirable}`, 'DEBUG');

  if (errors.length > 0) {
    appendLog(`Validation failed for Customer ID ${apiData.id}: ${errors.join('; ')}`, 'ERROR');
    throw new Error(errors.join('; '));
  }

  // Map to DTO
  const customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any } = {
    id: apiData.id.toString(),
    email: apiData.email,
    has_account: false,
    default_billing_address_id: null,
    default_shipping_address_id: null,
    company_name: null,
    first_name: apiData.name ? apiData.name.split(' ')[0] || null : null,
    last_name: apiData.name ? apiData.name.split(' ').slice(1).join(' ') || null : null,
    addresses: apiData.city ? [{
      address_1: apiData.city,
      city: apiData.city,
      country_code: 'IN',
      postal_code: apiData.pin || '',
      province: apiData.state || '',
      customer_id: apiData.id.toString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_default_shipping: true,
      is_default_billing: true,
    }] : [],
    phone: apiData.mobile || null,
    groups: [],
    additional_data: {
      totalOrders,
      totalSales,
      non_expirable_loyalty_points: loyaltyPerpetual,
      expirable_loyalty_points: loyaltyExpirable,
      notes: apiData.notes,
      last_order_created_at: apiData.last_order_created_at,
      type: apiData.type,
    },
    metadata: {},
    created_by: 'api-migration-script',
    deleted_at: null,
    created_at: apiData.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  appendLog(`Successfully validated Customer ID ${apiData.id}: mapped to DTO with ${customer.addresses.length} addresses`, 'DEBUG');
  logOperation('validateAPICustomer_completed', {
    customerId: customer.id,
    hasAddresses: customer.addresses.length > 0,
    additionalDataKeys: Object.keys(customer.additional_data)
  });

  return customer;
};

// === Terminal Input ===
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});
const prompt = (query: string) => new Promise<string>((resolve) => rl.question(query, resolve));

// === Base CURL Command ===
const BASE_CURL_COMMAND = (page: number) => `
curl -s 'https://api-enterprise.mydukaan.io/api/store/seller/store-buyer/?ordering=-orders_total_cost&page=${page}&page_size=30' \
-H 'Authorization: Bearer ${TOKEN}' \
-H 'User-Agent: Mozilla/5.0' \
-H 'Accept: application/json' \
-H 'x-Mode: seller-web' \
-H 'x-dukaan-store-id: 202299269' \
--compressed
`;

// === Error Logging (moved to enhanced logging functions above) ===

// === cURL Execution ===
async function executeCURL(page: number): Promise<any[]> {
  logOperation('executeCURL_start', { page });
  appendLog(`Starting cURL execution for page ${page}`, 'DEBUG');

  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    exec(BASE_CURL_COMMAND(page), async (error, stdout, stderr) => {
      const executionTime = Date.now() - startTime;

      if (error) {
        const errorMsg = `Error on page ${page}: ${error.message}`;
        appendLog(`cURL execution failed for page ${page} after ${executionTime}ms: ${error.message}`, 'ERROR');
        await logError('executeCURL', error, { page, executionTime });
        reject(errorMsg);
        return;
      }

      if (stderr && !stderr.trim().startsWith('% Total')) {
        const errorMsg = `Stderr on page ${page}: ${stderr}`;
        appendLog(`cURL stderr for page ${page}: ${stderr}`, 'WARN');
        reject(errorMsg);
        return;
      }

      try {
        appendLog(`Parsing JSON response for page ${page} (execution time: ${executionTime}ms)`, 'DEBUG');
        const json = JSON.parse(stdout);

        if (json.detail && json.detail.includes('Invalid token')) {
          const errorMsg = `Token expired on page ${page}`;
          appendLog(errorMsg, 'ERROR');
          reject({ type: '401', message: errorMsg });
          return;
        }

        if (Array.isArray(json.results)) {
          appendLog(`Successfully fetched ${json.results.length} results from page ${page} in ${executionTime}ms`, 'INFO');
          logOperation('executeCURL_success', { page, resultCount: json.results.length, executionTime });
          resolve(json.results);
        } else {
          appendLog(`No results array found in response for page ${page}`, 'WARN');
          logOperation('executeCURL_empty', { page, executionTime });
          resolve([]); // Empty results
        }
      } catch (err) {
        const errorMsg = `Failed to parse JSON on page ${page}: ${err}`;
        appendLog(`JSON parsing failed for page ${page}: ${err}`, 'ERROR');
        await logError('executeCURL_parse', err, { page, executionTime });
        reject(errorMsg);
      }
    });
  });
}

// === Database Migration Setup ===
let servicePack: servicePack;
let container: any;

// === Batch Fetch Logic ===
export default async function fetchAllPages({ container: containerArg }: ExecArgs) {
  logOperation('fetchAllPages_start', {});
  appendLog('Starting batch fetch process', 'INFO');

  // Set up services
  appendLog('Resolving services from container', 'DEBUG');
  const customerService = containerArg.resolve('customer');
  const extendedCustomerService = containerArg.resolve('extended_customer');
  const linkService = containerArg.resolve('link');
  const query = containerArg.resolve(ContainerRegistrationKeys.QUERY);

  servicePack = {
    customerService,
    extendedCustomerService,
    link: linkService,
    query,
  };
  container = containerArg;

  appendLog('Services resolved successfully', 'DEBUG');
  logOperation('services_resolved', {
    hasCustomerService: !!customerService,
    hasExtendedCustomerService: !!extendedCustomerService,
    hasLinkService: !!linkService,
    hasQuery: !!query
  });

  const batchSize = 10;
  appendLog(`Configured batch size: ${batchSize}`, 'INFO');

  // Ask user for starting page
  let inputPage = await prompt('Enter the starting page number (default 1): ');
  inputPage = inputPage.trim();
  let page = 1;
  if (inputPage) {
    const parsed = parseInt(inputPage, 10);
    if (isNaN(parsed) || parsed < 1) {
      appendLog(`Invalid input "${inputPage}", starting from page 1.`, 'WARN');
      console.log(`Invalid input "${inputPage}", starting from page 1.`);
    } else {
      page = parsed;
      appendLog(`Starting from user-specified page: ${page}`, 'INFO');
    }
  }

  logOperation('batch_fetch_start', { startingPage: page, batchSize });
  console.log('🚀 Starting batch fetch...');
  appendLog(`Starting batch fetch from page ${page} with batch size ${batchSize}`, 'INFO');

  let totalProcessedCustomers = 0;
  let totalBatches = 0;
  const migrationStartTime = Date.now();

  while (true) {
    totalBatches++;
    const batchStartTime = Date.now();

    console.log(`🔎 Preparing batch starting from page ${page}...`);
    appendLog(`Preparing batch ${totalBatches} starting from page ${page}`, 'INFO');

    const batchPages = Array.from({ length: batchSize }, (_, i) => page + i);
    console.log(`➡️ Batch pages:`, batchPages);
    logOperation('batch_pages_prepared', { batchNumber: totalBatches, pages: batchPages });

    if (batchPages.length === 0) {
      console.log('✅ No more pages to fetch.');
      appendLog('No more pages to fetch - ending batch processing', 'INFO');
      break;
    }

    appendLog(`Executing cURL requests for ${batchPages.length} pages`, 'DEBUG');
    const promises = batchPages.map((p) =>
      executeCURL(p).catch((err) => {
        console.error(`❌ Error in page ${p}:`, err);
        appendLog(`Error in page ${p}: ${err}`, 'ERROR');
        return [];
      })
    );

    const results = await Promise.all(promises);
    const flatResults = results.flat();
    const batchFetchTime = Date.now() - batchStartTime;

    logOperation('batch_fetch_completed', {
      batchNumber: totalBatches,
      pagesRequested: batchPages.length,
      totalResults: flatResults.length,
      fetchTime: batchFetchTime
    });

    if (flatResults.length === 0) {
      console.log('✅ No more results. Fetch complete.');
      appendLog('No more results found - fetch complete', 'INFO');
      break;
    }

    console.log(`📦 Processing batch (${flatResults.length} records)...`);
    appendLog(`Processing batch ${totalBatches} with ${flatResults.length} records (fetch time: ${batchFetchTime}ms)`, 'INFO');

    // Process each customer in the batch
    const CUSTOMER_BATCH_SIZE = 5; // Smaller batch for database operations
    appendLog(`Processing ${flatResults.length} customers in sub-batches of ${CUSTOMER_BATCH_SIZE}`, 'DEBUG');

    let batchProcessedCustomers = 0;
    const customerProcessingStartTime = Date.now();

    for (let i = 0; i < flatResults.length; i += CUSTOMER_BATCH_SIZE) {
      const customerBatch = flatResults.slice(i, i + CUSTOMER_BATCH_SIZE);
      const subBatchNumber = Math.floor(i / CUSTOMER_BATCH_SIZE) + 1;

      appendLog(`Processing customer sub-batch ${subBatchNumber} (${customerBatch.length} customers)`, 'DEBUG');
      logOperation('customer_sub_batch_start', {
        batchNumber: totalBatches,
        subBatchNumber,
        customerCount: customerBatch.length
      });

      await Promise.all(
        customerBatch.map(async (apiCustomer: APICustomerData, index) => {
          const customerIndex = i + index + 1;
          const globalCustomerIndex = totalProcessedCustomers + customerIndex;

          try {
            appendLog(`Processing customer ${globalCustomerIndex} (ID: ${apiCustomer.id})`, 'DEBUG');
            const validatedCustomer = await validateAPICustomer(apiCustomer);
            const msg = await createItemInDB(validatedCustomer, servicePack, container);
            const successMsg = `Customer ${globalCustomerIndex}: ${msg}`;
            console.log(successMsg);
            appendLog(successMsg);
            logOperation('customer_processed_success', {
              customerId: apiCustomer.id,
              globalIndex: globalCustomerIndex,
              message: msg
            });
            batchProcessedCustomers++;
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
            const failMsg = `Customer ${globalCustomerIndex}: ERROR - ${errorMsg}`;
            console.error(failMsg);
            appendLog(failMsg, 'ERROR');
            await logError('Customer Processing', error, {
              customerId: apiCustomer.id,
              customerIndex: globalCustomerIndex,
              batchNumber: totalBatches,
              subBatchNumber
            });
          }
        })
      );

      logOperation('customer_sub_batch_completed', {
        batchNumber: totalBatches,
        subBatchNumber,
        processedCount: customerBatch.length
      });
    }

    totalProcessedCustomers += batchProcessedCustomers;
    const customerProcessingTime = Date.now() - customerProcessingStartTime;
    const totalBatchTime = Date.now() - batchStartTime;

    appendLog(`Batch ${totalBatches} completed: processed ${batchProcessedCustomers} customers in ${customerProcessingTime}ms (total batch time: ${totalBatchTime}ms)`, 'INFO');
    logOperation('batch_completed', {
      batchNumber: totalBatches,
      customersProcessed: batchProcessedCustomers,
      totalCustomersProcessed: totalProcessedCustomers,
      processingTime: customerProcessingTime,
      totalBatchTime
    });

    page += batchSize;
  }

  const migrationEndTime = Date.now();
  const totalMigrationTime = migrationEndTime - migrationStartTime;

  console.log(`🎉 Migration complete.`);
  appendLog(`Migration complete. Total customers processed: ${totalProcessedCustomers} across ${totalBatches} batches in ${totalMigrationTime}ms`, 'INFO');
  logOperation('migration_completed', {
    totalCustomersProcessed: totalProcessedCustomers,
    totalBatches,
    finalPage: page,
    totalMigrationTime
  });

  rl.close(); // Close readline interface when done
}

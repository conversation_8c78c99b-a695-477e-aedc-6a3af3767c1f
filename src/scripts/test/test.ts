import { exec } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as readline from 'readline';
import { CustomerAddressDTO, CustomerDTO, ExecArgs, ICustomerModuleService, RemoteQueryFunction } from '@medusajs/framework/types';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { createItemInDB } from '../utils/customers';
import { Link } from '@medusajs/framework/modules-sdk';
import ExtendedCustomerService from '../../modules/extended/customer/service';

const ERROR_LOG = path.join(__dirname, 'errors.log');
let TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kYpsFwj_dqywOhUnw1ZY0V9q5A4bDvpdra2KN2BPgyw'; // Replace with your token

export type CustomerDTOWithModifiedAddress<T extends keyof CustomerAddressDTO> = Omit<CustomerDTO, "addresses"> & {
    addresses: Omit<CustomerAddressDTO, T>[];
};

interface servicePack {
  customerService: ICustomerModuleService;
  extendedCustomerService: ExtendedCustomerService;
  link: Link;
  query: Omit<RemoteQueryFunction, symbol>
}

// 👇 API Customer Data Interface
export interface APICustomerData {
  mobile: string;
  id: number;
  uuid: string;
  city: string;
  email: string;
  buyer_id: number;
  notes: string | null;
  buyer_uuid: string;
  state: string;
  pin: string;
  address_line: string;
  name: string;
  type: string;
  created_at: string;
  credit_balance: number | null;
  expirable_balance: number | null;
  tags: string[];
  orders_total_cost: number;
  orders: number;
  last_order_created_at: string;
}

// 👇 Append logs to a file
const appendLog = (message: string) => {
  const logPath = path.join(__dirname, 'migration-log.txt');
  require('fs').appendFileSync(logPath, message + '\n');
};

// 👇 Validate function: maps API data to CustomerDTO
const validateAPICustomer = async (apiData: APICustomerData): Promise<CustomerDTOWithModifiedAddress<'id'> & { additional_data: any }> => {
  const errors: string[] = [];

  if (!apiData.id) errors.push('Missing Customer ID');
  if (!apiData.mobile) errors.push('Missing Phone Number');

  const totalOrders = apiData.orders || 0;
  const totalSales = apiData.orders_total_cost || 0;

  const loyaltyPerpetual = Number(parseFloat((apiData.credit_balance || 0).toString()).toFixed(3));
  const loyaltyExpirable = Number(parseFloat((apiData.expirable_balance || 0).toString()).toFixed(3));

  const tags = apiData.tags || [];

  if (errors.length > 0) {
    throw new Error(errors.join('; '));
  }

  // Map to DTO
  const customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any } = {
    id: apiData.id.toString(),
    email: apiData.email,
    has_account: false,
    default_billing_address_id: null,
    default_shipping_address_id: null,
    company_name: null,
    first_name: apiData.name ? apiData.name.split(' ')[0] || null : null,
    last_name: apiData.name ? apiData.name.split(' ').slice(1).join(' ') || null : null,
    addresses: apiData.city ? [{
      address_1: apiData.city,
      city: apiData.city,
      country_code: 'IN',
      postal_code: apiData.pin || '',
      province: apiData.state || '',
      customer_id: apiData.id.toString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_default_shipping: true,
      is_default_billing: true,
    }] : [],
    phone: apiData.mobile || null,
    groups: [],
    additional_data: {
      totalOrders,
      totalSales,
      non_expirable_loyalty_points: loyaltyPerpetual,
      expirable_loyalty_points: loyaltyExpirable,
      notes : apiData.notes,
      last_order_created_at : apiData.last_order_created_at,
      type : apiData.type,
    },
    metadata: {},
    created_by: 'api-migration-script',
    deleted_at: null,
    created_at: apiData.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  return customer;
};

// === Terminal Input ===
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});
const prompt = (query: string) => new Promise<string>((resolve) => rl.question(query, resolve));

// === Base CURL Command ===
const BASE_CURL_COMMAND = (page: number) => `
curl -s 'https://api-enterprise.mydukaan.io/api/store/seller/store-buyer/?ordering=-orders_total_cost&page=${page}&page_size=30' \
-H 'Authorization: Bearer ${TOKEN}' \
-H 'User-Agent: Mozilla/5.0' \
-H 'Accept: application/json' \
-H 'x-Mode: seller-web' \
-H 'x-dukaan-store-id: 202299269' \
--compressed
`;

// === Error Logging ===
async function logError(message: string): Promise<void> {
  const log = `${new Date().toISOString()} - ${message}\n`;
  await fs.appendFile(ERROR_LOG, log);
}

// === cURL Execution ===
async function executeCURL(page: number): Promise<any[]> {
  return new Promise((resolve, reject) => {
    exec(BASE_CURL_COMMAND(page), async (error, stdout, stderr) => {
      if (error) {
        reject(`Error on page ${page}: ${error.message}`);
        return;
      }

      if (stderr && !stderr.trim().startsWith('% Total')) {
        reject(`Stderr on page ${page}: ${stderr}`);
        return;
      }

      try {
        const json = JSON.parse(stdout);
        if (json.detail && json.detail.includes('Invalid token')) {
          reject({ type: '401', message: `Token expired on page ${page}` });
          return;
        }
        if (Array.isArray(json.results)) {
          resolve(json.results);
        } else {
          resolve([]); // Empty results
        }
      } catch (err) {
        reject(`Failed to parse JSON on page ${page}: ${err}`);
      }
    });
  });
}

// === Database Migration Setup ===
let servicePack: servicePack;
let container: any;

// === Batch Fetch Logic ===
export default async function fetchAllPages({ container: containerArg }: ExecArgs) {
  // Set up services
  const customerService = containerArg.resolve('customer');
  const extendedCustomerService = containerArg.resolve('extended_customer');
  const linkService = containerArg.resolve('link');
  const query = containerArg.resolve(ContainerRegistrationKeys.QUERY);

  servicePack = {
    customerService,
    extendedCustomerService,
    link: linkService,
    query,
  };
  container = containerArg;

  const batchSize = 10;

  // Ask user for starting page
  let inputPage = await prompt('Enter the starting page number (default 1): ');
  inputPage = inputPage.trim();
  let page = 1;
  if (inputPage) {
    const parsed = parseInt(inputPage, 10);
    if (isNaN(parsed) || parsed < 1) {
      console.log(`Invalid input "${inputPage}", starting from page 1.`);
    } else {
      page = parsed;
    }
  }

  console.log('🚀 Starting batch fetch...');

  while (true) {
    console.log(`🔎 Preparing batch starting from page ${page}...`);
    const batchPages = Array.from({ length: batchSize }, (_, i) => page + i);
    console.log(`➡️ Batch pages:`, batchPages);

    if (batchPages.length === 0) {
      console.log('✅ No more pages to fetch.');
      break;
    }

    const promises = batchPages.map((p) =>
      executeCURL(p).catch((err) => {
        console.error(`❌ Error in page ${p}:`, err);
        return [];
      })
    );

    const results = await Promise.all(promises);
    const flatResults = results.flat();

    if (flatResults.length === 0) {
      console.log('✅ No more results. Fetch complete.');
      break;
    }

    console.log(`📦 Processing batch (${flatResults.length} records)...`);
    appendLog(`Processing batch of ${flatResults.length} records`);

    // Process each customer in the batch
    const CUSTOMER_BATCH_SIZE = 5; // Smaller batch for database operations
    for (let i = 0; i < flatResults.length; i += CUSTOMER_BATCH_SIZE) {
      const customerBatch = flatResults.slice(i, i + CUSTOMER_BATCH_SIZE);
      await Promise.all(
        customerBatch.map(async (apiCustomer: APICustomerData, index) => {
          const customerIndex = i + index + 1;
          try {
            const validatedCustomer = await validateAPICustomer(apiCustomer);
            const msg = await createItemInDB(validatedCustomer, servicePack, container);
            const successMsg = `Customer ${customerIndex}: ${msg}`;
            console.log(successMsg);
            appendLog(successMsg);
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
            const failMsg = `Customer ${customerIndex}: ERROR - ${errorMsg}`;
            console.error(failMsg);
            appendLog(failMsg);
            await logError(`Customer ID ${apiCustomer.id}: ${errorMsg}`);
          }
        })
      );
    }

    page += batchSize;
  }

  console.log(`🎉 Migration complete.`);
  appendLog('Migration complete.');
  rl.close(); // Close readline interface when done
}
